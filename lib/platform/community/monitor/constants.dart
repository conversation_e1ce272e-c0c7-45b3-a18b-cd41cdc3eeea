const String diy_Productkey_PCS = 'HB-PCS'; // diy pcs
const String diy_Productkey_BMS = 'HB-BMS'; // diy bms
const String diy_Productkey_GW = 'HB-EMS-GW'; // diy ems 网关
const String diy_Productkey_EMS = 'HB-EMS'; // diy ems 设备
const String diy_Productkey_PLUG = 'HB-PLUG'; // diy 插座
const String diy_Productkey_DC = 'HB-DC'; // diy DC
const String diy_point_power = '50491393'; // pcs 光伏
const String diy_point_ac_power = '50394113'; // pcs 电网
const String diy_point_otherLoads = '50590721'; // pcs 负载
const String diy_point_gridConnStatus = '55206913'; // pcs 并网状态
const String diy_point_bms_I = '33606657'; //电流
const String diy_point_bms_U = '33605633'; //电压
const String diy_point_plugin = '100769793'; //插座
const String diy_point_ups = ' 188902401'; //ups
const String diy_point_enable_config = '23375873'; // 使能配置

enum EnableConfigType {
  /// 使能配置
  enable(1),

  /// 禁用配置
  disable(0);

  final int value;

  const EnableConfigType(this.value);
}

const int DIY_FLOW_DIRECTION_MAX = 3; // 3作为正数边界值
const int DIY_FLOW_DIRECTION_MIN = -3; // -3作为负数边界值

const int DIY_FLOW_DIRECTION_TO_CENTER = 2; //往一体机流动
const int DIY_FLOW_DIRECTION_TO_OUT = 1; // 从一体机往外流动
const int DIY_FLOW_DIRECTION_NONE = 0; // 不流动
const int DIY_FLOW_DIRECTION_NO_DEVICE = -1; // 没有设备
const int DIY_FLOW_SWITCH_OPEN = 1; // 开关打开
const int DIY_FLOW_SWITCH_CLOSE = 0; // 开关关闭
/// 0311 新能流图点位
/// EMS
const String diy_point_pv_total_power =
    '16932865'; //能流图上 pv 总功率大于0，流向一体机, 值为0，不流动。
const String diy_point_pv_children_power = '50490369'; // pv-1 点击后下钻 pv 的功率
const String diy_point_pv_children2_power = '50490370'; // pv-2
const String diy_point_ac_output_power =
    '16933889'; // ac output 功率 大于0，一体机流向AC Output；值为0，不流动。
const String diy_point_ac_output_switch = '23120897'; // ac output 开关值
const String diy_point_dc_output_power =
    '16937985'; // dc output 功率 大于0，一体机流向DC Output；值为0，不流动。

// 22版本更新DC读写测点分离, 写用这个测点
const String diy_point_dc_output_status_write = '23121921'; // dc output 总开关值
// DC 读用这个测点
const String diy_point_dc_output_status_read = '191100929'; //DC负载开关 1开 0关

const String diy_point_dc_output_typec1_switch =
    '191096833'; // dc output typec-1 开关值
const String diy_point_dc_output_typec1_power =
    '188897281'; // dc output typec-1 功率
const String diy_point_dc_output_typec2_switch =
    '191097857'; // dc output typec-2 开关值
const String diy_point_dc_output_typec2_power =
    '188898305'; // dc output typec-2 功率
const String diy_point_dc_output_usb_switch =
    '191098881'; // dc output typec-2 开关值
const String diy_point_dc_output_usb_power =
    '188899329'; // dc output typec-2 功率
const String diy_point_plug_count = '21541889'; // 插座数量
const String diy_point_plug_total_power = '16935937'; // 插座功率
const String diy_point_plug_children_switch = '104960001'; // 每个插座开关状态
const String diy_point_plug_children_power = '100769793'; //每个插座功率
const String diy_point_plug_switch_control = '106110977'; // 下发开关控制点位
const String diy_point_CT_status = '21547009'; // ct连接状态 1:连接正常；0：连接异常
const String diy_point_ct_power =
    '16930817'; // ac main 总功率 正值表示一体机流向电网/AC main，负值表示电网/AC main流向一体机，0 不动
const String diy_point_on_grid_power =
    '16934913'; // AC-onGrid 的功率 正是一体机往外，负是其他设备流向一体机，0 不动
const String diy_point_other_load = '16936961'; // 其他负载总功率 ，正值往设备，负值往中央设备
const String diy_point_SOC = '21548033'; // 系统 soc
const String diy_point_current_remaining_power = '21549057'; // 系统 当前剩余电量
const String diy_point_battery_status =
    '21556225'; // 电池冲放状态 电池总功率的正负值, 正值表示电池向外放电, 负值表示电池正在充电
const String diy_point_machine_status =
    '21552129'; // EMS 工作状态 0：正常 1：等待 2：告警 3：故障 4：待机,5:低功耗 8:强制充电
const String diy_point_grid_connect_status = '21545985'; // 并网状态 0 离网，1并网
const String diy_point_runMode = '23132161'; // 系统运行模式 2 自发自用模式 3 电池优先模式
const String diy_point_battery_count = '21542913'; //加电包数量

/// EMS固件版本
const String diy_point_ems_firmware_version = '25167873';

/// EMS硬件版本
const String diy_point_ems_hardware_version = '25166849';

/// PCS固件版本
const String diy_point_pcs_firmware_version = '58722305';

/// PCS硬件版本
const String diy_point_pcs_hardware_version = '58721281';

/// 自发自用充电上限  默认100%，取值范围50%-100%
const String diy_point_self_use_charge_limit = '23135233';

/// 自发自用放电下限 默认10%，取值范围5%-49%
const String diy_point_self_use_discharge_limit = '23136257';

/// 电池优先电池充电SOC上限
const String diy_point_battery_charge_soc_limit = '23137281';

/// 电池优先电池放电SOC下限
const String diy_point_battery_discharge_soc_limit = '23138305';

/// Boot 固件版本
const String diy_point_ems_boot_version = '25753601';

/// ESP32 固件版本
const String diy_point_ems_esp32_version = '25749505';

/// BMS
const String diy_point_bms = '37839873'; // 电池 状态 3 是 充电 4 是放电
const String diy_point_bms_soc = '37801985'; //电池百分比
const String diy_point_bms_current_power = '33627137'; //电池当前电量
/// 故障点位
const String diy_point_fault = '21534721'; // 故障点位
const String diy_point_warning = '21535745'; // 告警点位

const String diy_point_ems_turn_off_system = '23133185'; // Turn Off System
const String diy_point_ems_restart = '22027265'; // 重启

const diy_point_create_update_plug_timer = '107108353'; //创建 or 更新  插座定时器
const diy_point_delete_plug_timer = '107108353'; //删除插座 定时器

/// 定时器点位
const diy_point_plug_children_time_start_point = '105013249';

/// 插座通信状态 0正常 1异常
const diy_point_plug_communication_status = '104857601';

/// dc板通信状态 0正常 1异常
const diy_point_dc_communication_status = '188743681';

/// CT 相位
const diy_point_ct_phase = '123939841';

/// CT 低功耗模式
const diy_point_ct_low_mode = '123942913';

/// CT 相位反转
const diy_point_ct_phase_reverse = '123941889';

/// CT 连接状态
const diy_point_ct_connect_status = '121634817';

/// CT 软件版本
const diy_point_ct_soft_version = '125831169';

/// CT 硬件版本
const diy_point_ct_hard_version = '125830145';

/// 电池定时充电时段XX, 最大十个
const List<String> diy_point_battery_charge_time_slot_array = [
  "23146497",
  "23146498",
  "23146499",
  "23146500",
  "23146501",
  "23146502",
  "23146503",
  "23146504",
  "23146505",
  "23146506"
];

/// 电池定时放电时段XX ,最大十个
const diy_point_battery_discharge_timeslot_array = [
  "23147521",
  "23147522",
  "23147523",
  "23147524",
  "23147525",
  "23147526",
  "23147527",
  "23147528",
  "23147529",
  "23147530"
];

/// 允许馈网：开关，默认开启，允许馈网；
const String diy_point_allow_grid_connect = '23134209';

///功率上限设置，数字文本，最大800W
const String diy_point_power_limit = '23286785';

/// other load 点位
const String diy_point_other_load_timeslot_1 = '23122945';
const List<String> diy_point_other_load_timeslot_array = [
  '23123969',
  '23123970',
  '23123971',
  '23123972',
  '23123973',
  '23123974',
  '23123975',
  '23123976',
  '23123977',
  '23123978'
];

const List<String> diy_point_other_load_power_array = [
  '23122945',
  '23122946',
  '23122947',
  '23122948',
  '23122949',
  '23122950',
  '23122951',
  '23122952',
  '23122953',
  '23122954'
];

/// 全量点位 ———————————— start
const List<String> diy_point_ems = [
  diy_point_pv_total_power,
  diy_point_ac_output_power,
  diy_point_ac_output_switch,
  diy_point_dc_output_power,
  // diy_point_dc_output_master_switch,
  diy_point_plug_count,
  diy_point_plug_total_power,
  diy_point_CT_status,
  diy_point_ct_power,
  diy_point_other_load,
  diy_point_SOC,
  diy_point_current_remaining_power,
  diy_point_battery_status,
  diy_point_machine_status,
  diy_point_grid_connect_status,
  diy_point_on_grid_power,
  diy_point_runMode,
  diy_point_ems_turn_off_system,
  diy_point_warning,
  diy_point_fault,
  ...diy_point_other_load_timeslot_array,
  ...diy_point_other_load_power_array,
  ...diy_point_battery_charge_time_slot_array,
  ...diy_point_battery_discharge_timeslot_array,
  diy_point_power_limit,
  ...diy_point_dc,
  diy_point_allow_grid_connect,
  diy_point_enable_config,
];

const List<String> diy_point_PCS = [
  diy_point_pv_children_power,
  diy_point_pv_children2_power,
];
const List<String> diy_point_BMS = [
  diy_point_bms,
  diy_point_bms_soc,
  diy_point_bms_current_power
];

const List<String> diy_point_PLUG = [
  diy_point_plug_children_switch,
  diy_point_plug_children_power
];

const List<String> diy_point_dc = [
  diy_point_dc_output_status_read,
  diy_point_dc_output_status_write,
  diy_point_dc_output_typec1_switch,
  diy_point_dc_output_typec1_power,
  diy_point_dc_output_typec2_switch,
  diy_point_dc_output_typec2_power,
  diy_point_dc_output_usb_switch,
  diy_point_dc_output_usb_power,
  diy_point_dc_communication_status,
];

/// 并机点位
const String diy_point_combine_pv_total_power = '17501185';
const String diy_point_combine_ac_output_power = '17502209';
const String diy_point_combine_ac_output_switch = '23120897';
const String diy_point_combine_dc_output_power = '17505281';
const String diy_point_combine_plug_count = '21692417';
const String diy_point_combine_plug_total_power = '17507329';
const String diy_point_combine_CT_status = '21691393';
const String diy_point_combine_ct_power = '17503233';
const String diy_point_combine_other_load = '17504257';
const String diy_point_combine_SOC = '21693441';
const String diy_point_combine_current_remaining_power = '21694465';
const String diy_point_combine_machine_status = '21688321';
const String diy_point_combine_fault = '21534721';
const String diy_point_combine_warning = '21535745';
const String diy_point_combine_grid_connect_status = '21689345';
const String diy_point_combine_on_grid_power = '17506305';
const String diy_point_combine_runMode = '23787521';
const String diy_point_combine_battery_count = '21703681';
// Add this list for all combine points
const List<String> diy_point_combine = [
  diy_point_combine_pv_total_power,
  diy_point_combine_ac_output_power,
  diy_point_combine_ac_output_switch,
  diy_point_combine_dc_output_power,
  diy_point_combine_plug_count,
  diy_point_combine_plug_total_power,
  diy_point_combine_CT_status,
  diy_point_combine_ct_power,
  diy_point_combine_other_load,
  diy_point_combine_SOC,
  diy_point_combine_current_remaining_power,
  diy_point_combine_machine_status,
  diy_point_combine_fault,
  diy_point_combine_warning,
  diy_point_combine_grid_connect_status,
  diy_point_combine_on_grid_power,
  diy_point_combine_runMode,
  diy_point_combine_battery_count,
  // 召唤主机的 点位
  diy_point_ems_turn_off_system,
  ...diy_point_other_load_timeslot_array,
  ...diy_point_other_load_power_array,
  ...diy_point_battery_charge_time_slot_array,
  ...diy_point_battery_discharge_timeslot_array,
  diy_point_enable_config,
];

/// 烟感点位

// 通讯状态
const String diy_point_smoke_communication_status = '356515841';

// 软件版本
const String diy_point_smoke_soft_version = '360712193';

/// SN码
const String diy_point_smoke_sn = '360713217';
// 厂家信息
const String diy_point_smoke_factory_info = '360714241';
// 设备型号
const String diy_point_smoke_model = '360715265';
// 设备时间
const String diy_point_smoke_time = '360716289';

// 告警
const String diy_point_smoke_warning = '356618241';
// 电池电压
const String diy_point_smoke_battery_voltage = '356619265';
// 电池容量
const String diy_point_smoke_battery_capacity = '356620289';
// 静音状态
const String diy_point_smoke_mute_status = '356621313';
// 静音设置
const String diy_point_smoke_mute_setting = '357670913';

/// 水浸点位

// 通讯状态
const String diy_point_flood_communication_status = '356515841';

// 软件版本
const String diy_point_flood_soft_version = '360712193';

/// SN码
const String diy_point_flood_sn = '360713217';
// 厂家信息
const String diy_point_flood_factory_info = '360714241';
// 设备型号
const String diy_point_flood_model = '360715265';
// 设备时间
const String diy_point_flood_time = '360716289';

// 告警
const String diy_point_flood_warning = '356618241';
// 电池电压
const String diy_point_flood_battery_voltage = '356619265';
// 电池容量
const String diy_point_flood_battery_capacity = '356620289';
// 静音状态
const String diy_point_flood_mute_status = '356621313';
// 静音设置
const String diy_point_flood_mute_setting = '357670913';

/// 温湿度传感器点位

// 通讯状态
const String diy_point_htg_communication_status = '356515841';

// 软件版本
const String diy_point_htg_soft_version = '360712193';

/// SN码
const String diy_point_htg_sn = '360713217';
// 厂家信息
const String diy_point_htg_factory_info = '360714241';
// 设备型号
const String diy_point_htg_model = '360715265';
// 设备时间
const String diy_point_htg_time = '360716289';

// 告警
const String diy_point_htg_warning = '356618241';
// 电池电压
const String diy_point_htg_battery_voltage = '356619265';
// 电池容量
const String diy_point_htg_battery_capacity = '356620289';
// 静音状态
const String diy_point_htg_mute_status = '356621313';
// 静音设置
const String diy_point_htg_mute_setting = '357670913';
