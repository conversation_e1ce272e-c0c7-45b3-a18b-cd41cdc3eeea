// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'monitor_quick_control_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MonitorQuickControlModel _$MonitorQuickControlModelFromJson(
        Map<String, dynamic> json) =>
    MonitorQuickControlModel(
      plugSwitch: (json['plugSwitch'] as List<dynamic>?)
          ?.map((e) => PlugSwitch.fromJson(e as Map<String, dynamic>))
          .toList(),
      dcProperty: (json['dcProperty'] as List<dynamic>?)
          ?.map((e) => DCPropertyModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      emsProperty: json['emsProperty'] == null
          ? null
          : EMSPropertyModel.fromJson(
              json['emsProperty'] as Map<String, dynamic>),
      clustered: json['clustered'] as int?,
    );

Map<String, dynamic> _$MonitorQuickControlModelToJson(
        MonitorQuickControlModel instance) =>
    <String, dynamic>{
      'plugSwitch': instance.plugSwitch,
      'dcProperty': instance.dcProperty,
      'emsProperty': instance.emsProperty,
      'clustered': instance.clustered,
    };

DCPropertyModel _$DCPropertyModelFromJson(Map<String, dynamic> json) =>
    DCPropertyModel(
      deviceNo: json['deviceNo'] as String?,
      deviceName: json['deviceName'] as String?,
      modelKey: json['modelKey'] as String?,
      productKey: json['productKey'] as String?,
      upsDCLoadSwitchIndetifier: json['upsDCLoadSwitchIndetifier'] as String?,
      upsDCLoadSwitchLanguageKey: json['upsDCLoadSwitchLanguageKey'] as String?,
      dcOutEnable: json['dcOutEnable'] as int?,
      slaverList: (json['slaverList'] as List<dynamic>?)
          ?.map((e) => DCPropertyModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    )..deviceStatus = json['deviceStatus'] as int;

Map<String, dynamic> _$DCPropertyModelToJson(DCPropertyModel instance) =>
    <String, dynamic>{
      'deviceNo': instance.deviceNo,
      'deviceName': instance.deviceName,
      'modelKey': instance.modelKey,
      'productKey': instance.productKey,
      'upsDCLoadSwitchIndetifier': instance.upsDCLoadSwitchIndetifier,
      'upsDCLoadSwitchLanguageKey': instance.upsDCLoadSwitchLanguageKey,
      'dcOutEnable': instance.dcOutEnable,
      'deviceStatus': instance.deviceStatus,
      'slaverList': instance.slaverList,
    };

EMSPropertyModel _$EMSPropertyModelFromJson(Map<String, dynamic> json) =>
    EMSPropertyModel(
      deviceNo: json['deviceNo'] as String?,
      deviceName: json['deviceName'] as String?,
      modelKey: json['modelKey'] as String?,
      productKey: json['productKey'] as String?,
      turnOffIdentifier: json['turnOffIdentifier'] as String?,
      turnOffIdentifierLanguageKey:
          json['turnOffIdentifierLanguageKey'] as String?,
      workModeIdentifier: json['workModeIdentifier'] as String?,
      workModeIdentifierLanguageKey:
          json['workModeIdentifierLanguageKey'] as String?,
      workModeValue: json['workModeValue'] as String?,
      acOutEnableIdentifier: json['acOutEnableIdentifier'] as String?,
      acOutEnableIdentifierLanguageKey:
          json['acOutEnableIdentifierLanguageKey'] as String?,
      acOutEnableValue: json['acOutEnableValue'] as String?,
      deviceStatus: json['deviceStatus'] as int?,
      turnOffValue: json['turnOffValue'] as String? ?? '2',
      deviceRebootIdentifier: json['deviceRebootIdentifier'] as String? ?? '',
      deviceWorkStatus: $enumDecodeNullable(
              _$DeviceWorkStatusEnumMap, json['deviceWorkStatus']) ??
          DeviceWorkStatus.normal,
      enableConfig: json['enableConfig'] as int? ?? 4294967295,
      slaverList: (json['slaverList'] as List<dynamic>?)
          ?.map((e) => EMSPropertyModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$EMSPropertyModelToJson(EMSPropertyModel instance) =>
    <String, dynamic>{
      'deviceNo': instance.deviceNo,
      'deviceName': instance.deviceName,
      'modelKey': instance.modelKey,
      'productKey': instance.productKey,
      'turnOffIdentifier': instance.turnOffIdentifier,
      'turnOffIdentifierLanguageKey': instance.turnOffIdentifierLanguageKey,
      'workModeIdentifier': instance.workModeIdentifier,
      'workModeIdentifierLanguageKey': instance.workModeIdentifierLanguageKey,
      'workModeValue': instance.workModeValue,
      'acOutEnableIdentifier': instance.acOutEnableIdentifier,
      'acOutEnableIdentifierLanguageKey':
          instance.acOutEnableIdentifierLanguageKey,
      'acOutEnableValue': instance.acOutEnableValue,
      'enableConfig': instance.enableConfig,
      'deviceWorkStatus': _$DeviceWorkStatusEnumMap[instance.deviceWorkStatus],
      'turnOffValue': instance.turnOffValue,
      'deviceRebootIdentifier': instance.deviceRebootIdentifier,
      'deviceStatus': instance.deviceStatus,
      'slaverList': instance.slaverList,
    };

const _$DeviceWorkStatusEnumMap = {
  DeviceWorkStatus.normal: 0,
  DeviceWorkStatus.wait: 1,
  DeviceWorkStatus.alarm: 2,
  DeviceWorkStatus.fault: 3,
  DeviceWorkStatus.standby: 4,
  DeviceWorkStatus.offline: -1,
  DeviceWorkStatus.eco: 5,
  DeviceWorkStatus.lock: 6,
};
