import 'package:json_annotation/json_annotation.dart';

import '../../system_repository/model/devices/ems_device_model.dart';
import 'plug_switch.dart';

part 'monitor_quick_control_model.g.dart';

@JsonSerializable()
class MonitorQuickControlModel {
  List<PlugSwitch>? plugSwitch;
  List<DCPropertyModel>? dcProperty;
  EMSPropertyModel? emsProperty;
  int? clustered;
  MonitorQuickControlModel({
    this.plugSwitch,
    this.dcProperty,
    this.emsProperty,
    this.clustered,
  });

  @override
  String toString() {
    return 'MonitorQuickControlModel(plugSwitch: $plugSwitch, dcProperty: $dcProperty, emsProperty: $emsProperty)';
  }

  factory MonitorQuickControlModel.fromJson(Map<String, dynamic> json) {
    return _$MonitorQuickControlModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$MonitorQuickControlModelToJson(this);
}

@JsonSerializable()
class DCPropertyModel {
  String? deviceNo;
  String? deviceName;
  String? modelKey;
  String? productKey;
  String? upsDCLoadSwitchIndetifier;
  String? upsDCLoadSwitchLanguageKey;
  int? dcOutEnable;
  // 设备状态 0正常  1离线
  int deviceStatus = 0;
  List<DCPropertyModel>? slaverList;

  DCPropertyModel(
      {this.deviceNo,
      this.deviceName,
      this.modelKey,
      this.productKey,
      this.upsDCLoadSwitchIndetifier,
      this.upsDCLoadSwitchLanguageKey,
      this.dcOutEnable,
      this.slaverList});

  factory DCPropertyModel.fromJson(Map<String, dynamic> json) {
    return _$DCPropertyModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$DCPropertyModelToJson(this);
}

@JsonSerializable()
class EMSPropertyModel {
  String? deviceNo;
  String? deviceName;
  String? modelKey;
  String? productKey;
  String? turnOffIdentifier;
  String? turnOffIdentifierLanguageKey;
  String? workModeIdentifier;
  String? workModeIdentifierLanguageKey;
  String? workModeValue;
  String? acOutEnableIdentifier;
  String? acOutEnableIdentifierLanguageKey;
  String? acOutEnableValue;
  int? enableConfig;

  /// 运行状态
  DeviceWorkStatus? deviceWorkStatus;

  /// 开关机状态  1关机 2开机
  String turnOffValue;

  /// 重启指令
  String deviceRebootIdentifier;

  /// 设备状态 0未激活 1正常 2离线
  int? deviceStatus;

  List<EMSPropertyModel>? slaverList;

  EMSPropertyModel(
      {this.deviceNo,
      this.deviceName,
      this.modelKey,
      this.productKey,
      this.turnOffIdentifier,
      this.turnOffIdentifierLanguageKey,
      this.workModeIdentifier,
      this.workModeIdentifierLanguageKey,
      this.workModeValue,
      this.acOutEnableIdentifier,
      this.acOutEnableIdentifierLanguageKey,
      this.acOutEnableValue,
      this.deviceStatus,
      this.turnOffValue = '2',
      this.deviceRebootIdentifier = '',
      this.deviceWorkStatus = DeviceWorkStatus.normal,
      this.enableConfig = 4294967295,
      this.slaverList});

  factory EMSPropertyModel.fromJson(Map<String, dynamic> json) {
    return _$EMSPropertyModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$EMSPropertyModelToJson(this);
}
